
> Configure project :
Fabric Loom: 1.10.5

> Task :compileJava
Note: Some input files use unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.

> Task :processResources UP-TO-DATE
> Task :classes
> Task :jar
> Task :compileTestJava NO-SOURCE
> Task :processIncludeJars UP-TO-DATE
> Task :sourcesJar
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE
> Task :test NO-SOURCE
> Task :validateAccessWidener NO-SOURCE
> Task :check UP-TO-DATE
> Task :cleanRun
> Task :generateLog4jConfig UP-TO-DATE
> Task :generateRemapClasspath UP-TO-DATE
> Task :generateDLIConfig UP-TO-DATE
> Task :configureLaunch UP-TO-DATE
> Task :remapSourcesJar FAILED
> Task :remapJar

[Incubating] Problems report is available at: file:///home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/reports/problems/problems-report.html

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':remapSourcesJar'.
> A failure occurred while executing net.fabricmc.loom.task.RemapSourcesJarTask$RemapSourcesAction
   > Failed to remap sources

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 20s
11 actionable tasks: 6 executed, 5 up-to-date
